#!/usr/bin/env python3
"""
Test script for the JIDA Last-Mile Delivery Optimizer
This script tests the main functionality without running the full analysis
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """Test basic functionality of the optimizer"""
    print("🧪 Testing JIDA Last-Mile Delivery Optimizer...")
    
    try:
        # Import the optimizer
        from jida_project_jul_13_complete_ml_development import LastMileDeliveryOptimizer
        
        # Initialize optimizer
        print("✅ Successfully imported LastMileDeliveryOptimizer")
        optimizer = LastMileDeliveryOptimizer()
        print("✅ Successfully initialized optimizer")
        
        # Test data generation
        print("\n📊 Testing data generation...")
        data = optimizer.generate_sample_data(n_samples=1000)  # Small sample for testing
        print(f"✅ Generated {len(data)} sample records")
        print(f"✅ Data columns: {list(data.columns)}")
        
        # Test preprocessing
        print("\n🔧 Testing data preprocessing...")
        X_scaled, y = optimizer.preprocess_data()
        print(f"✅ Preprocessed data shape: {X_scaled.shape}")
        print(f"✅ Target shape: {y.shape}")
        
        # Test model building (without training)
        print("\n🧠 Testing LSTM model building...")
        model = optimizer.build_lstm_model((10, X_scaled.shape[1]))
        print("✅ Successfully built LSTM model")
        print(f"✅ Model summary: {model.count_params()} parameters")
        
        # Test model export functionality
        print("\n💾 Testing model export...")
        optimizer.lstm_model = model  # Set the model
        optimizer.export_trained_model()
        print("✅ Model export completed")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gradio_interface():
    """Test Gradio interface creation"""
    print("\n🚀 Testing Gradio interface...")
    
    try:
        from jida_project_jul_13_complete_ml_development import LastMileDeliveryOptimizer
        
        optimizer = LastMileDeliveryOptimizer()
        
        # Generate minimal data for testing
        optimizer.generate_sample_data(n_samples=100)
        optimizer.preprocess_data()
        
        # Build model (don't train)
        optimizer.lstm_model = optimizer.build_lstm_model((10, optimizer.X_scaled.shape[1]))
        
        print("✅ Gradio interface setup ready")
        print("📝 Note: To launch interface, call optimizer.launch_simple_gradio_interface()")
        
        return True
        
    except Exception as e:
        print(f"❌ Gradio test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("JIDA DELIVERY OPTIMIZER - TEST SUITE")
    print("=" * 60)
    
    # Run basic functionality tests
    basic_test_passed = test_basic_functionality()
    
    # Run Gradio interface test
    gradio_test_passed = test_gradio_interface()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Basic Functionality: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"Gradio Interface: {'✅ PASSED' if gradio_test_passed else '❌ FAILED'}")
    
    if basic_test_passed and gradio_test_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 Next Steps:")
        print("1. Run the full analysis: python jida_project_jul_13_complete_ml_development.py")
        print("2. Or test individual components as needed")
        print("3. Check the 'models' folder for exported model files")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")
    
    print("=" * 60)
